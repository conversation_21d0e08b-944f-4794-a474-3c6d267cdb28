import { IsNotEmpty, IsString, IsNumber, IsOptional, IsDateString, IsArray, ValidateNested, Min, Max, IsBoolean } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CostCenterAllocationDto } from './create-lancamento-financeiro.dto';

export class CreateDespesaDto {
  // Campos obrigatórios
  @ApiProperty({ description: 'ID da pessoa/fornecedor' })
  @IsNotEmpty({ message: 'Fornecedor/Pessoa é obrigatório' })
  @IsNumber()
  pessoaId: number;

  @ApiProperty({ description: 'Descrição da despesa', minLength: 1, maxLength: 200 })
  @IsNotEmpty({ message: 'Descrição é obrigatória' })
  @IsString()
  descricao: string;

  @ApiProperty({ description: 'Valor da despesa', minimum: 0.01 })
  @IsNotEmpty({ message: 'Valor é obrigatório' })
  @IsNumber()
  @Min(0.01, { message: 'Valor deve ser maior que zero' })
  valor: number;

  // Campos opcionais da aba Básico
  @ApiPropertyOptional({ description: 'Data de pagamento da despesa', format: 'date' })
  @IsOptional()
  @IsDateString()
  dataPagamento?: string;

  @ApiPropertyOptional({ description: 'Data de competência da despesa', format: 'date' })
  @IsOptional()
  @IsDateString()
  dataCompetencia?: string;

  @ApiPropertyOptional({ description: 'ID da conta bancária/caixa' })
  @IsOptional()
  @IsNumber()
  contaId?: number;

  @ApiPropertyOptional({ description: 'ID do local/filial' })
  @IsOptional()
  @IsNumber()
  localId?: number;

  // Campos da aba Financeiro
  @ApiPropertyOptional({ description: 'ID da categoria do lançamento financeiro' })
  @IsOptional()
  @IsNumber()
  categoriaLctoFinanceiroId?: number;

  @ApiPropertyOptional({ 
    description: 'Alocações por centro de custo',
    type: [CostCenterAllocationDto]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CostCenterAllocationDto)
  alocacoesCentroCusto?: CostCenterAllocationDto[];

  // Campos da aba Adicional
  @ApiPropertyOptional({ description: 'ID do plano de contas crédito' })
  @IsOptional()
  @IsNumber()
  planoContaCredito?: number;

  @ApiPropertyOptional({ description: 'ID do fornecedor' })
  @IsOptional()
  @IsNumber()
  fornecedorId?: number;

  @ApiPropertyOptional({ description: 'Observações adicionais', maxLength: 1000 })
  @IsOptional()
  @IsString()
  observacao?: string;

  // Campos de repetição
  @ApiPropertyOptional({ description: 'Indica se a despesa deve ser repetida' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value === 'true' || value === '1';
    }
    return Boolean(value);
  })
  repetirDespesa?: boolean;

  @ApiPropertyOptional({ 
    description: 'Tipo de repetição',
    enum: ['este', 'esteEProximos']
  })
  @IsOptional()
  @IsString()
  tipoRepeticao?: 'este' | 'esteEProximos';

  @ApiPropertyOptional({ 
    description: 'Periodicidade da repetição',
    enum: ['mensal', 'semanal', 'anual']
  })
  @IsOptional()
  @IsString()
  periodicidade?: 'mensal' | 'semanal' | 'anual';

  @ApiPropertyOptional({ description: 'Quantidade de repetições', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  quantidadeRepeticoes?: number;

  // Campos internos (definidos pelo controller)
  @ApiPropertyOptional({ description: 'ID da empresa (preenchido automaticamente)' })
  @IsOptional()
  @IsNumber()
  empresaId?: number;

  @ApiPropertyOptional({ description: 'Tipo de lançamento financeiro (preenchido automaticamente como 2 para despesa)' })
  @IsOptional()
  @IsNumber()
  tipoLancamentoFinanceiroId?: number;
}
