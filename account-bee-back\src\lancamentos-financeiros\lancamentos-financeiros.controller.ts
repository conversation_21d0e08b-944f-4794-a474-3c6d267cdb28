import {
  Controller,
  Get,
  Post,
  Put,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { LancamentosFinanceirosService } from './lancamentos-financeiros.service';
import {
  CreateLancamentoFinanceiroDto,
  CreateDespesaDto,
  LancamentoFinanceiroResponseDto,
  CostCenterAllocationDto
} from './dto';

@ApiTags('lancamentos-financeiros')
@Controller('lancamentos-financeiros')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class LancamentosFinanceirosController {
  constructor(
    private readonly lancamentosService: LancamentosFinanceirosService,
  ) {}

  @Post('receitas')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Criar nova receita (lançamento financeiro tipo 1)' })
  @ApiResponse({
    status: 201,
    description: 'Receita criada com sucesso',
    type: LancamentoFinanceiroResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async createReceita(
    @Body() createDto: CreateLancamentoFinanceiroDto,
    @Request() req: any,
  ): Promise<LancamentoFinanceiroResponseDto> {
    // Garantir que é uma receita
    createDto.tipoLancamentoFinanceiroId = 1;
    createDto.empresaId = req.user.empresaId;

    return this.lancamentosService.create(createDto, req.user);
  }

  @Post('despesas')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Criar nova despesa (lançamento financeiro tipo 2)' })
  @ApiResponse({
    status: 201,
    description: 'Despesa criada com sucesso',
    type: LancamentoFinanceiroResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async createDespesa(
    @Body() createDespesaDto: CreateDespesaDto,
    @Request() req: any,
  ): Promise<LancamentoFinanceiroResponseDto> {
    // Converter DTO de despesa para DTO de lançamento financeiro
    const createDto: CreateLancamentoFinanceiroDto = {
      ...createDespesaDto,
      // Mapear campos específicos de despesa
      dataLancamento: createDespesaDto.dataPagamento,
      planoContaCredito: createDespesaDto.planoContaCredito, // Corrigido: usar planoContaCredito
      repetirReceita: createDespesaDto.repetirDespesa,
      // Garantir que é uma despesa
      tipoLancamentoFinanceiroId: 2,
      empresaId: req.user.empresaId,
    };

    return this.lancamentosService.create(createDto, req.user);
  }

  @Get('listar/:tipo')
  @ApiOperation({ summary: 'Listar lançamentos financeiros por tipo (receitas ou despesas)' })
  @ApiResponse({
    status: 200,
    description: 'Lista de lançamentos financeiros',
    type: [LancamentoFinanceiroResponseDto]
  })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async listar(
    @Param('tipo') tipo: 'receitas' | 'despesas',
    @Request() req: any,
    @Query('mes') mes?: string,
    @Query('ano') ano?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ): Promise<any> {
    try {
      console.log('🔍 Controller - Listando lançamentos:', { tipo, mes, ano, page, limit });
      console.log('🔍 Controller - Usuário:', req.user?.email, 'EmpresaId:', req.user?.empresaId);

      const empresaId = req.user.empresaId;
      const tipoLancamento = tipo === 'receitas' ? 1 : 2;

      const filtros = {
        mes: mes ? parseInt(mes) : new Date().getMonth() + 1,
        ano: ano ? parseInt(ano) : new Date().getFullYear(),
        page: page ? parseInt(page) : 1,
        limit: limit ? parseInt(limit) : 50,
        tipoLancamentoFinanceiroId: tipoLancamento,
      };

      console.log('🔍 Controller - Filtros processados:', filtros);

      const resultado = await this.lancamentosService.listar(empresaId, filtros);
      console.log('✅ Controller - Resultado obtido:', { total: resultado.total, dataLength: resultado.data?.length });

      return resultado;
    } catch (error) {
      console.error('❌ Controller - Erro ao listar lançamentos:', error);
      throw error;
    }
  }

  @Get('calcular-valores/:tipo')
  @ApiOperation({ summary: 'Calcular valores totais de despesas e receitas' })
  @ApiResponse({
    status: 200,
    description: 'Valores calculados',
  })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async calcularValores(
    @Param('tipo') tipo: 'receitas' | 'despesas',
    @Request() req: any,
    @Query('mes') mes?: string,
    @Query('ano') ano?: string,
  ): Promise<any> {
    const empresaId = req.user.empresaId;
    const tipoLancamento = tipo === 'receitas' ? 1 : 2;

    const filtros = {
      mes: mes ? parseInt(mes) : new Date().getMonth() + 1,
      ano: ano ? parseInt(ano) : new Date().getFullYear(),
      tipoLancamentoFinanceiroId: tipoLancamento,
    };

    return this.lancamentosService.calcularValores(empresaId, filtros);
  }

  @Get('extrato/listar')
  @ApiOperation({ summary: 'Listar extrato unificado (receitas e despesas)' })
  @ApiResponse({
    status: 200,
    description: 'Extrato unificado',
  })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async listarExtrato(
    @Request() req: any,
    @Query('mes') mes?: string,
    @Query('ano') ano?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ): Promise<any> {
    try {
      console.log('🔍 Controller - Listando extrato unificado:', { mes, ano, page, limit });

      const empresaId = req.user.empresaId;

      const filtros = {
        mes: mes ? parseInt(mes) : new Date().getMonth() + 1,
        ano: ano ? parseInt(ano) : new Date().getFullYear(),
        page: page ? parseInt(page) : 1,
        limit: limit ? parseInt(limit) : 50,
      };

      console.log('🔍 Controller - Filtros processados:', filtros);

      const resultado = await this.lancamentosService.listarExtrato(empresaId, filtros);
      console.log('✅ Controller - Extrato obtido:', { total: resultado.total, dataLength: resultado.data?.length });

      return resultado;
    } catch (error) {
      console.error('❌ Controller - Erro ao listar extrato:', error);
      throw error;
    }
  }

  @Patch(':id/efetivado')
  @ApiOperation({ summary: 'Alterar status efetivado do lançamento financeiro' })
  @ApiResponse({
    status: 200,
    description: 'Status alterado com sucesso',
    type: LancamentoFinanceiroResponseDto
  })
  @ApiResponse({ status: 404, description: 'Lançamento financeiro não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async alterarStatusEfetivado(
    @Param('id') id: string,
    @Body() body: { efetivado: boolean },
    @Request() req: any,
  ): Promise<LancamentoFinanceiroResponseDto> {
    try {
      console.log('🔍 Controller - Alterando status efetivado:', { id, efetivado: body.efetivado });

      const empresaId = req.user.empresaId;
      const resultado = await this.lancamentosService.alterarStatusEfetivado(+id, body.efetivado, empresaId, req.user);

      console.log('✅ Controller - Status alterado com sucesso');
      return resultado;
    } catch (error) {
      console.error('❌ Controller - Erro ao alterar status:', error);
      throw error;
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar lançamento financeiro por ID' })
  @ApiParam({ name: 'id', description: 'ID do lançamento financeiro' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lançamento encontrado', 
    type: LancamentoFinanceiroResponseDto 
  })
  @ApiResponse({ status: 404, description: 'Lançamento não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async findOne(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<LancamentoFinanceiroResponseDto> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.findById(+id, empresaId);
  }

  @Put(':id/alocacoes-centro-custo')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Atualizar alocações de centro de custo de um lançamento' })
  @ApiParam({ name: 'id', description: 'ID do lançamento financeiro' })
  @ApiResponse({ status: 204, description: 'Alocações atualizadas com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Lançamento não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async updateCostCenterAllocations(
    @Param('id') id: string,
    @Body() allocations: CostCenterAllocationDto[],
    @Request() req: any,
  ): Promise<void> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.updateCostCenterAllocations(+id, allocations, empresaId, req.user);
  }

  @Get(':id/alocacoes-centro-custo')
  @ApiOperation({ summary: 'Listar alocações de centro de custo de um lançamento' })
  @ApiParam({ name: 'id', description: 'ID do lançamento financeiro' })
  @ApiResponse({ status: 200, description: 'Lista de alocações' })
  @ApiResponse({ status: 404, description: 'Lançamento não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async getCostCenterAllocations(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<any[]> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.getCostCenterAllocations(+id, empresaId);
  }

  @Delete(':lancamentoId/alocacoes-centro-custo/:allocationId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Excluir uma alocação de centro de custo específica' })
  @ApiParam({ name: 'lancamentoId', description: 'ID do lançamento financeiro' })
  @ApiParam({ name: 'allocationId', description: 'ID da alocação de centro de custo' })
  @ApiResponse({ status: 204, description: 'Alocação excluída com sucesso' })
  @ApiResponse({ status: 404, description: 'Alocação não encontrada' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async deleteCostCenterAllocation(
    @Param('lancamentoId') lancamentoId: string,
    @Param('allocationId') allocationId: string,
    @Request() req: any,
  ): Promise<void> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.deleteCostCenterAllocation(+lancamentoId, +allocationId, empresaId, req.user);
  }
}
