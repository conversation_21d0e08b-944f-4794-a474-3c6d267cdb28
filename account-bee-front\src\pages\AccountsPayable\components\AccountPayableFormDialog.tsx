import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Upload, X } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/shadcn/tabs';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/forms';
import { Input, CurrencyInput, Select, DateInput } from '@/components/ui/forms';
import { Button } from '@/components/ui/forms';
import { Textarea } from '@/components/ui/shadcn/textarea';
import { Checkbox } from '@/components/ui/forms';
import { RadioGroup, RadioGroupItem } from '@/components/ui/forms';
import { Label } from '@/components/ui/forms';
import { CostCenterApportionment, type CostCenterAllocation } from '@/components/ui/forms';
import { useAccountsPayable } from '@/hooks/useAccountsPayable';
import { useCategories } from '@/hooks/useCategories';
import { useLocations } from '@/hooks/useLocations';
import { useCNAE } from '@/hooks/useCNAE';
import { useCostCenters } from '@/hooks/useCostCenters';
import { useSuppliers } from '@/hooks/useSuppliers';
import { BankAccountSelect } from '@/components/ui/forms';
import { AccountPayable, TaxInfo } from '@/types/accounts';

const taxSchema = z.object({
  name: z.string(),
  rate: z.number().min(0).max(100),
  value: z.number().min(0),
  withheld: z.boolean(),
});

const costCenterAllocationSchema = z.object({
  id: z.string(),
  costCenterId: z.string(),
  costCenterName: z.string(),
  value: z.number().min(0),
  percentage: z.number().min(0).max(100),
});

const accountSchema = z.object({
  supplier: z.string().min(1, 'Fornecedor é obrigatório'),
  description: z.string().min(5, 'Descrição deve ter pelo menos 5 caracteres'),
  amount: z.number().min(0.01, 'Valor deve ser maior que zero'), // Apenas um campo valor (sem valorBruto)
  dueDate: z.string().min(1, 'Data de vencimento é obrigatória'),
  competenceDate: z.string().optional(),
  account: z.string().optional(),
  location: z.string().optional(),
  category: z.string().optional(),
  chartAccountDebit: z.string().optional(),
  provider: z.string().optional(),
  cnae: z.string().optional(),
  costCenter: z.string().optional(),
  percentage: z.number().min(0).max(100).optional(),
  value: z.number().min(0).optional(),
  observation: z.string().optional(),
  recurring: z.boolean().optional(),
  recurringType: z.enum(['this', 'thisAndNext']).optional(),
  taxes: z.array(taxSchema).optional(),
  costCenterAllocations: z.array(costCenterAllocationSchema).optional(),
});

type AccountFormData = z.infer<typeof accountSchema>;

interface AccountPayableFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  account?: AccountPayable | null;
  onSuccess: () => void;
}

const defaultTaxes: TaxInfo[] = [
  { name: 'ISS', rate: 0, value: 0, withheld: false },
  { name: 'PIS', rate: 0, value: 0, withheld: false },
  { name: 'COFINS', rate: 0, value: 0, withheld: false },
  { name: 'INSS', rate: 0, value: 0, withheld: false },
  { name: 'IR', rate: 0, value: 0, withheld: false },
  { name: 'CSLL', rate: 0, value: 0, withheld: false },
  { name: 'CPP', rate: 0, value: 0, withheld: false },
];

export function AccountPayableFormDialog({ open, onOpenChange, account, onSuccess }: AccountPayableFormDialogProps) {
  const { createAccount, updateAccount, loading } = useAccountsPayable();
  const { categories } = useCategories();
  const { locations } = useLocations();
  const { cnaes } = useCNAE();
  const { costCenters } = useCostCenters();
  const { suppliers } = useSuppliers();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [taxes, setTaxes] = useState<TaxInfo[]>(defaultTaxes);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [costCenterAllocations, setCostCenterAllocations] = useState<CostCenterAllocation[]>([]);

  const form = useForm<AccountFormData>({
    resolver: zodResolver(accountSchema),
    defaultValues: {
      supplier: '',
      description: '',
      amount: 0,
      dueDate: '',
      competenceDate: '',
      account: '',
      location: '',
      category: '',
      chartAccountDebit: '',
      provider: '',
      cnae: '',
      costCenter: '',
      percentage: 0,
      value: 0,
      observation: '',
      recurring: false,
      recurringType: 'this',
      taxes: defaultTaxes,
      costCenterAllocations: [],
    },
  });

  useEffect(() => {
    if (account) {
      form.reset({
        supplier: account.supplier,
        description: account.description,
        amount: account.amount || 0,
        dueDate: account.dueDate,
        competenceDate: account.competenceDate || '',
        account: account.account?.id || '',
        location: account.location || '',
        category: account.category?.id || '',
        chartAccountDebit: account.chartAccountDebit || '',
        provider: account.provider || '',
        cnae: account.cnae?.id || '',
        costCenter: account.costCenter?.id || '',
        percentage: account.percentage || 0,
        value: account.value || 0,
        observation: account.observation || '',
        recurring: account.recurring || false,
        recurringType: account.recurringType || 'this',
      });
      setTaxes(account.taxes || defaultTaxes);
      setCostCenterAllocations((account as any).costCenterAllocations || []);
    } else {
      form.reset({
        supplier: '',
        description: '',
        amount: 0,
        dueDate: '',
        competenceDate: '',
        account: '',
        location: '',
        category: '',
        chartAccountDebit: '',
        provider: '',
        cnae: '',
        costCenter: '',
        percentage: 0,
        value: 0,
        observation: '',
        recurring: false,
        recurringType: 'this',
      });
      setTaxes(defaultTaxes);
      setAttachments([]);
      setCostCenterAllocations([]);
    }
  }, [account, form]);

  const handleTaxChange = (index: number, field: keyof TaxInfo, value: any) => {
    const newTaxes = [...taxes];
    newTaxes[index] = { ...newTaxes[index], [field]: value };
    setTaxes(newTaxes);
  };

  const calculateTotals = () => {
    const totalRetainedRate = taxes.filter(tax => tax.withheld).reduce((sum, tax) => sum + tax.rate, 0);
    const totalRetainedValue = taxes.filter(tax => tax.withheld).reduce((sum, tax) => sum + tax.value, 0);
    const totalRate = taxes.reduce((sum, tax) => sum + tax.rate, 0);
    const totalValue = taxes.reduce((sum, tax) => sum + tax.value, 0);

    return { totalRetainedRate, totalRetainedValue, totalRate, totalValue };
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachments(prev => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: AccountFormData) => {
    setIsSubmitting(true);
    try {
      const formData = {
        ...data,
        taxes,
        attachments: attachments.map(file => file.name),
        costCenterAllocations,
      };

      if (account) {
        await updateAccount(account.id, formData);
      } else {
        await createAccount(formData as any);
      }
      onSuccess();
    } catch (error) {
      console.error('Erro ao salvar conta:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const totals = calculateTotals();

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-6xl max-w-[90vw] h-[90vh] max-h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader>
          <DialogTitle>
            {account ? 'Editar Conta a Pagar' : 'Nova Despesa'}
          </DialogTitle>
          <DialogDescription>
            {account 
              ? 'Atualize as informações da conta.' 
              : 'Insira as informações abaixo para cadastrar seu lançamento.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex-1 flex flex-col min-h-0">
            <Tabs defaultValue="basic" className="flex-1 flex flex-col min-h-0">
              <TabsList className="grid w-full grid-cols-4 shrink-0">
                <TabsTrigger value="basic">Básico</TabsTrigger>
                <TabsTrigger value="financial">Financeiro</TabsTrigger>
                <TabsTrigger value="additional">Adicional</TabsTrigger>
                <TabsTrigger value="taxes">Impostos</TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-y-auto min-h-0 max-h-full">
                <TabsContent value="basic" className="space-y-4 mt-6 pb-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="supplier"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Fornecedor *</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                              placeholder="Nome do fornecedor"
                              options={suppliers.map(supplier => ({
                                value: supplier.id,
                                label: supplier.descricao,
                              }))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem className="md:col-span-2">
                          <FormLabel>Descrição *</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Descrição detalhada do lançamento" 
                              {...field} 
                              className="min-h-[80px] resize-y"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="dueDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Data de Vencimento *</FormLabel>
                          <FormControl>
                            <DateInput
                              value={field.value}
                              onChange={field.onChange}
                              placeholder="Selecione a data"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="competenceDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Data de Competência</FormLabel>
                          <FormControl>
                            <DateInput
                              value={field.value || ''}
                              onChange={field.onChange}
                              placeholder="Selecione a data"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="financial" className="space-y-4 mt-6 pb-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="account"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Conta</FormLabel>
                          <FormControl>
                            <BankAccountSelect
                              value={field.value}
                              onValueChange={field.onChange}
                              placeholder="Selecione a conta"
                              showBalance={true}
                              filterActive={true}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="location"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Local</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                              placeholder="Selecione o local"
                              options={locations.map(location => ({
                                value: location.id.toString(),
                                label: location.nome,
                              }))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                    <FormField
                      control={form.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Valor *</FormLabel>
                          <FormControl>
                            <CurrencyInput
                              value={field.value || 0}
                              onChange={field.onChange}
                              placeholder="R$ 0,00"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Categoria</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                              placeholder="Selecione a categoria"
                              options={categories.map(category => ({
                                value: category.id,
                                label: category.name,
                              }))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Alocação por Centro de Custo */}
                  <CostCenterApportionment
                    costCenters={costCenters}
                    totalAmount={form.watch('amount') || 0}
                    allocations={costCenterAllocations}
                    onAllocationsChange={setCostCenterAllocations}
                    className="border-t pt-4"
                  />

                  {/* Repetir Despesa */}
                  <div className="space-y-4 border-t pt-4">
                    <FormField
                      control={form.control}
                      name="recurring"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              Repetir esta despesa
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />

                    {form.watch('recurring') && (
                      <FormField
                        control={form.control}
                        name="recurringType"
                        render={({ field }) => (
                          <FormItem className="space-y-3">
                            <FormLabel>Tipo de repetição</FormLabel>
                            <FormControl>
                              <RadioGroup
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                className="flex flex-col space-y-1"
                              >
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="this" id="this" />
                                  <Label htmlFor="this">Apenas este mês</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="thisAndNext" id="thisAndNext" />
                                  <Label htmlFor="thisAndNext">Este mês e próximos</Label>
                                </div>
                              </RadioGroup>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="additional" className="space-y-4 mt-6 pb-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="chartAccountDebit"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Plano Conta Débito</FormLabel>
                          <FormControl>
                            <Input placeholder="Plano Conta Débito" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="provider"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Prestador</FormLabel>
                          <FormControl>
                            <Input placeholder="Nome do prestador" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="cnae"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>CNAE</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                              placeholder="Selecione o CNAE"
                              options={cnaes.map(cnae => ({
                                value: cnae.id,
                                label: `${cnae.code} - ${cnae.description}`,
                              }))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="costCenter"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Centro de Custo (Principal)</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                              placeholder="Selecione o centro de custo"
                              options={costCenters.map(center => ({
                                value: center.id.toString(),
                                label: center.descricao,
                              }))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="observation"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Observação</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Observações adicionais" 
                            {...field} 
                            className="min-h-[80px] resize-y"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Upload de Anexos */}
                  <div className="space-y-3">
                    <Label>Anexos</Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                      <div className="text-center">
                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="mt-4">
                          <label htmlFor="file-upload" className="cursor-pointer">
                            <span className="mt-2 block text-sm font-medium text-gray-900">
                              Clique para fazer upload ou arraste os arquivos aqui
                            </span>
                            <input
                              id="file-upload"
                              name="file-upload"
                              type="file"
                              className="sr-only"
                              multiple
                              onChange={handleFileUpload}
                            />
                          </label>
                          <p className="mt-1 text-xs text-gray-500">
                            PNG, JPG, PDF até 10MB
                          </p>
                        </div>
                      </div>
                    </div>

                    {attachments.length > 0 && (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Arquivos selecionados:</Label>
                        {attachments.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="text-sm text-gray-700">{file.name}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(index)}
                              className="h-6 w-6 p-0"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="taxes" className="space-y-4 mt-6 pb-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label className="text-base font-medium">Configuração de Impostos</Label>
                    </div>

                    <div className="border rounded-lg overflow-hidden">
                      <table className="w-full">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-2 text-left">Nome</th>
                            <th className="px-4 py-2 text-left">Alíquota (%)</th>
                            <th className="px-4 py-2 text-left">Valor (R$)</th>
                            <th className="px-4 py-2 text-left">Retido</th>
                          </tr>
                        </thead>
                        <tbody>
                          {taxes.map((tax, index) => (
                            <tr key={tax.name} className="border-t">
                              <td className="px-4 py-2 font-medium">{tax.name}</td>
                              <td className="px-4 py-2">
                                <Input
                                  type="number"
                                  min="0"
                                  max="100"
                                  step="0.01"
                                  value={tax.rate}
                                  onChange={(e) => handleTaxChange(index, 'rate', parseFloat(e.target.value) || 0)}
                                  className="w-24"
                                  placeholder="0,00"
                                />
                              </td>
                              <td className="px-4 py-2">
                                <div className="w-32">
                                  <CurrencyInput
                                    value={tax.value}
                                    onChange={(value) => handleTaxChange(index, 'value', value || 0)}
                                    placeholder="R$ 0,00"
                                  />
                                </div>
                              </td>
                              <td className="px-4 py-2">
                                <Checkbox
                                  checked={tax.withheld}
                                  onCheckedChange={(checked) => handleTaxChange(index, 'withheld', checked)}
                                />
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    {/* Resumo dos Impostos */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="font-medium text-blue-800 mb-2">Impostos Retidos</div>
                          <div className="space-y-1 text-blue-700">
                            <div>Alíquota Total: {totals.totalRetainedRate.toFixed(2)}%</div>
                            <div>Valor Total: {formatCurrency(totals.totalRetainedValue)}</div>
                          </div>
                        </div>
                        <div>
                          <div className="font-medium text-blue-800 mb-2">Total dos Impostos</div>
                          <div className="space-y-1 text-blue-700">
                            <div>Alíquota Total: {totals.totalRate.toFixed(2)}%</div>
                            <div>Valor Total: {formatCurrency(totals.totalValue)}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </div>

              <div className="flex justify-end space-x-2 pt-4 border-t shrink-0">
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Cancelar
                </Button>
                <Button type="submit" loading={isSubmitting || loading}>
                  {account ? 'Atualizar' : 'Criar Despesa'}
                </Button>
              </div>
            </Tabs>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 