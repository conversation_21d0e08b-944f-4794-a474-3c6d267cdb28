import { useState, useCallback, useRef } from 'react';

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
  message: string;
  progress?: number;
}

export interface LoadingOptions {
  message?: string;
  initialProgress?: number;
  onSuccess?: () => void;
  onError?: (error: string) => void;
  onComplete?: () => void;
}

export const useLoading = (initialState: boolean = false, initialMessage: string = 'Carregando...') => {
  const [state, setState] = useState<LoadingState>({
    isLoading: initialState,
    error: null,
    message: initialMessage,
    progress: undefined
  });

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startLoading = useCallback((options: LoadingOptions = {}) => {
    const { message = 'Carregando...', initialProgress } = options;
    
    setState({
      isLoading: true,
      error: null,
      message,
      progress: initialProgress
    });
  }, []);

  const stopLoading = useCallback((options: LoadingOptions = {}) => {
    const { onSuccess, onComplete } = options;
    
    setState(prev => ({
      ...prev,
      isLoading: false,
      progress: undefined
    }));

    if (onSuccess) onSuccess();
    if (onComplete) onComplete();
  }, []);

  const setError = useCallback((error: string, options: LoadingOptions = {}) => {
    const { onError, onComplete } = options;
    
    setState(prev => ({
      ...prev,
      isLoading: false,
      error,
      progress: undefined
    }));

    if (onError) onError(error);
    if (onComplete) onComplete();
  }, []);

  const updateProgress = useCallback((progress: number, message?: string) => {
    setState(prev => ({
      ...prev,
      progress: Math.min(100, Math.max(0, progress)),
      message: message || prev.message
    }));
  }, []);

  const updateMessage = useCallback((message: string) => {
    setState(prev => ({
      ...prev,
      message
    }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null
    }));
  }, []);

  const reset = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      message: initialMessage,
      progress: undefined
    });
  }, [initialMessage]);

  // Utility para executar operações assíncronas com loading
  const executeWithLoading = useCallback(async <T>(
    operation: () => Promise<T>,
    options: LoadingOptions = {}
  ): Promise<T> => {
    const { message = 'Carregando...', onSuccess, onError, onComplete } = options;
    
    try {
      startLoading({ message });
      const result = await operation();
      stopLoading({ onSuccess, onComplete });
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      setError(errorMessage, { onError, onComplete });
      throw error;
    }
  }, [startLoading, stopLoading, setError]);

  // Utility para criar loading com timeout
  const withTimeout = useCallback((
    duration: number,
    timeoutMessage: string = 'Operação demorou mais que o esperado'
  ) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      updateMessage(timeoutMessage);
    }, duration);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [updateMessage]);

  return {
    // Estado
    ...state,
    
    // Controles básicos
    startLoading,
    stopLoading,
    setError,
    clearError,
    reset,
    
    // Atualizações
    updateProgress,
    updateMessage,
    
    // Utilities
    executeWithLoading,
    withTimeout
  };
};

// Hook para múltiplos loadings
export const useMultipleLoading = () => {
  const [loadingStates, setLoadingStates] = useState<Record<string, LoadingState>>({});

  const getLoadingState = useCallback((key: string): LoadingState => {
    return loadingStates[key] || {
      isLoading: false,
      error: null,
      message: 'Carregando...',
      progress: undefined
    };
  }, [loadingStates]);

  const startLoading = useCallback((key: string, options: LoadingOptions = {}) => {
    const { message = 'Carregando...', initialProgress } = options;
    
    setLoadingStates(prev => ({
      ...prev,
      [key]: {
        isLoading: true,
        error: null,
        message,
        progress: initialProgress
      }
    }));
  }, []);

  const stopLoading = useCallback((key: string, options: LoadingOptions = {}) => {
    const { onSuccess, onComplete } = options;
    
    setLoadingStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        isLoading: false,
        progress: undefined
      }
    }));

    if (onSuccess) onSuccess();
    if (onComplete) onComplete();
  }, []);

  const setError = useCallback((key: string, error: string, options: LoadingOptions = {}) => {
    const { onError, onComplete } = options;
    
    setLoadingStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        isLoading: false,
        error,
        progress: undefined
      }
    }));

    if (onError) onError(error);
    if (onComplete) onComplete();
  }, []);

  const updateProgress = useCallback((key: string, progress: number, message?: string) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        progress: Math.min(100, Math.max(0, progress)),
        message: message || prev[key]?.message || 'Carregando...'
      }
    }));
  }, []);

  const clearLoading = useCallback((key: string) => {
    setLoadingStates(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  }, []);

  const clearAllLoading = useCallback(() => {
    setLoadingStates({});
  }, []);

  const isAnyLoading = Object.values(loadingStates).some(state => state.isLoading);
  const hasAnyError = Object.values(loadingStates).some(state => state.error);

  return {
    loadingStates,
    getLoadingState,
    startLoading,
    stopLoading,
    setError,
    updateProgress,
    clearLoading,
    clearAllLoading,
    isAnyLoading,
    hasAnyError
  };
};

// Hook para loading com debounce
export const useDebouncedLoading = (delay: number = 300) => {
  const [isLoading, setIsLoading] = useState(false);
  const [debouncedLoading, setDebouncedLoading] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startLoading = useCallback(() => {
    setIsLoading(true);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setDebouncedLoading(true);
    }, delay);
  }, [delay]);

  const stopLoading = useCallback(() => {
    setIsLoading(false);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    
    setDebouncedLoading(false);
  }, []);

  return {
    isLoading,
    debouncedLoading,
    startLoading,
    stopLoading
  };
};