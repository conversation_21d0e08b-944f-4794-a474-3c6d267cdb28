import { CostCenterAllocationDto } from './create-lancamento-financeiro.dto';
export declare class CreateDespesaDto {
    pessoaId: number;
    descricao: string;
    valor: number;
    dataPagamento?: string;
    dataCompetencia?: string;
    contaId?: number;
    localId?: number;
    categoriaLctoFinanceiroId?: number;
    alocacoesCentroCusto?: CostCenterAllocationDto[];
    planoContaCredito?: number;
    fornecedorId?: number;
    observacao?: string;
    repetirDespesa?: boolean;
    tipoRepeticao?: 'este' | 'esteEProximos';
    periodicidade?: 'mensal' | 'semanal' | 'anual';
    quantidadeRepeticoes?: number;
    empresaId?: number;
    tipoLancamentoFinanceiroId?: number;
}
