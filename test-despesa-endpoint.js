// Teste simples para verificar se o endpoint de despesas está funcionando
const fetch = require('node-fetch');

const testDespesaEndpoint = async () => {
  const baseUrl = 'http://localhost:3000';
  
  // Dados de teste para criar uma despesa
  const despesaData = {
    pessoaId: 1,
    descricao: 'Teste de despesa via API',
    valor: 100.50,
    dataPagamento: '2025-01-15',
    dataCompetencia: '2025-01-01',
    contaId: 1,
    localId: 1,
    categoriaLctoFinanceiroId: 1,
    observacao: 'Teste de integração do endpoint de despesas'
  };

  try {
    console.log('🔍 Testando endpoint POST /api/lancamentos-financeiros/despesas');
    console.log('📤 Dados enviados:', JSON.stringify(despesaData, null, 2));

    const response = await fetch(`${baseUrl}/api/lancamentos-financeiros/despesas`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Aqui você precisaria adicionar o token de autenticação real
        // 'Authorization': 'Bearer SEU_TOKEN_AQUI'
      },
      body: JSON.stringify(despesaData)
    });

    console.log('📊 Status da resposta:', response.status);
    console.log('📊 Headers da resposta:', Object.fromEntries(response.headers.entries()));

    const responseData = await response.text();
    console.log('📥 Resposta recebida:', responseData);

    if (response.ok) {
      console.log('✅ Endpoint de despesas funcionando corretamente!');
      try {
        const jsonData = JSON.parse(responseData);
        console.log('📋 Dados da despesa criada:', JSON.stringify(jsonData, null, 2));
      } catch (e) {
        console.log('⚠️ Resposta não é JSON válido');
      }
    } else {
      console.log('❌ Erro no endpoint:', response.status, responseData);
    }

  } catch (error) {
    console.error('💥 Erro ao testar endpoint:', error.message);
  }
};

// Executar o teste
testDespesaEndpoint();
