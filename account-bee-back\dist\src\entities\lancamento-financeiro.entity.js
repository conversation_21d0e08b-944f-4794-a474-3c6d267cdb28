"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LancamentoFinanceiro = void 0;
const typeorm_1 = require("typeorm");
const empresa_entity_1 = require("./empresa.entity");
const pessoa_entity_1 = require("./pessoa.entity");
const conta_entity_1 = require("./conta.entity");
const local_entity_1 = require("./local.entity");
const fornecedor_entity_1 = require("./fornecedor.entity");
const tipo_lancamento_financeiro_entity_1 = require("./tipo-lancamento-financeiro.entity");
const categoria_lancamento_entity_1 = require("./categoria-lancamento.entity");
const plano_conta_entity_1 = require("./plano-conta.entity");
const financeiro_centro_custo_entity_1 = require("./financeiro-centro-custo.entity");
let LancamentoFinanceiro = class LancamentoFinanceiro {
    id;
    descricao;
    dataLancamento;
    dataCompetencia;
    valorBruto;
    valor;
    observacao;
    efetivado;
    conciliado;
    idTransacaoBancaria;
    identificador;
    empresaId;
    pessoaId;
    contaId;
    localId;
    fornecedorId;
    tipoLancamentoFinanceiroId;
    categoriaLctoFinanceiroId;
    planoContaCredito;
    planoContaDebito;
    empresa;
    pessoa;
    conta;
    local;
    fornecedor;
    tipoLancamentoFinanceiro;
    categoriaLancamentoFinanceiro;
    planoContaCreditoEntity;
    planoContaDebitoEntity;
    financeiroCentroCustos;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    dataHoraUsuarioInc;
    dataSync;
    isExcluido;
    usuarioAlt;
    usuarioDel;
    usuarioInc;
    uuid;
};
exports.LancamentoFinanceiro = LancamentoFinanceiro;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], LancamentoFinanceiro.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRICAO', length: 200, nullable: false }),
    __metadata("design:type", String)
], LancamentoFinanceiro.prototype, "descricao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_LANCAMENTO', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], LancamentoFinanceiro.prototype, "dataLancamento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_COMPETENCIA', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], LancamentoFinanceiro.prototype, "dataCompetencia", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'VALOR_BRUTO', length: 500, nullable: true }),
    __metadata("design:type", String)
], LancamentoFinanceiro.prototype, "valorBruto", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'VALOR', length: 500, nullable: true }),
    __metadata("design:type", String)
], LancamentoFinanceiro.prototype, "valor", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'OBSERVACAO', length: 1000, nullable: true }),
    __metadata("design:type", String)
], LancamentoFinanceiro.prototype, "observacao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EFETIVADO', nullable: true }),
    __metadata("design:type", Boolean)
], LancamentoFinanceiro.prototype, "efetivado", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CONCILIADO', nullable: true }),
    __metadata("design:type", Boolean)
], LancamentoFinanceiro.prototype, "conciliado", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ID_TRASACAO_BANCARIA', length: 1000, nullable: true }),
    __metadata("design:type", String)
], LancamentoFinanceiro.prototype, "idTransacaoBancaria", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IDENTIFICADOR', length: 200, nullable: true }),
    __metadata("design:type", String)
], LancamentoFinanceiro.prototype, "identificador", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMPRESA_ID', nullable: false }),
    __metadata("design:type", Number)
], LancamentoFinanceiro.prototype, "empresaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PESSOA_ID', nullable: false }),
    __metadata("design:type", Number)
], LancamentoFinanceiro.prototype, "pessoaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CONTA_ID', nullable: true }),
    __metadata("design:type", Number)
], LancamentoFinanceiro.prototype, "contaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LOCAL_ID', nullable: true }),
    __metadata("design:type", Number)
], LancamentoFinanceiro.prototype, "localId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'FORNECEDOR', nullable: true }),
    __metadata("design:type", Number)
], LancamentoFinanceiro.prototype, "fornecedorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'TIPO_LANCAMENTO_FINANCEIRO_ID', nullable: false }),
    __metadata("design:type", Number)
], LancamentoFinanceiro.prototype, "tipoLancamentoFinanceiroId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CATEGORIA_LCTO_FINANCEIRO_ID', nullable: true }),
    __metadata("design:type", Number)
], LancamentoFinanceiro.prototype, "categoriaLctoFinanceiroId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PLANO_CONTA_CREDITO', nullable: true }),
    __metadata("design:type", Number)
], LancamentoFinanceiro.prototype, "planoContaCredito", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PLANO_CONTA_DEBITO', nullable: true }),
    __metadata("design:type", Number)
], LancamentoFinanceiro.prototype, "planoContaDebito", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => empresa_entity_1.Empresa),
    (0, typeorm_1.JoinColumn)({ name: 'EMPRESA_ID' }),
    __metadata("design:type", empresa_entity_1.Empresa)
], LancamentoFinanceiro.prototype, "empresa", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => pessoa_entity_1.Pessoa),
    (0, typeorm_1.JoinColumn)({ name: 'PESSOA_ID' }),
    __metadata("design:type", pessoa_entity_1.Pessoa)
], LancamentoFinanceiro.prototype, "pessoa", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => conta_entity_1.Conta),
    (0, typeorm_1.JoinColumn)({ name: 'CONTA_ID' }),
    __metadata("design:type", conta_entity_1.Conta)
], LancamentoFinanceiro.prototype, "conta", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => local_entity_1.Local),
    (0, typeorm_1.JoinColumn)({ name: 'LOCAL_ID' }),
    __metadata("design:type", local_entity_1.Local)
], LancamentoFinanceiro.prototype, "local", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => fornecedor_entity_1.Fornecedor),
    (0, typeorm_1.JoinColumn)({ name: 'FORNECEDOR' }),
    __metadata("design:type", fornecedor_entity_1.Fornecedor)
], LancamentoFinanceiro.prototype, "fornecedor", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => tipo_lancamento_financeiro_entity_1.TipoLancamentoFinanceiro),
    (0, typeorm_1.JoinColumn)({ name: 'TIPO_LANCAMENTO_FINANCEIRO_ID' }),
    __metadata("design:type", tipo_lancamento_financeiro_entity_1.TipoLancamentoFinanceiro)
], LancamentoFinanceiro.prototype, "tipoLancamentoFinanceiro", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => categoria_lancamento_entity_1.CategoriaLancamento),
    (0, typeorm_1.JoinColumn)({ name: 'CATEGORIA_LCTO_FINANCEIRO_ID' }),
    __metadata("design:type", categoria_lancamento_entity_1.CategoriaLancamento)
], LancamentoFinanceiro.prototype, "categoriaLancamentoFinanceiro", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => plano_conta_entity_1.PlanoConta),
    (0, typeorm_1.JoinColumn)({ name: 'PLANO_CONTA_CREDITO' }),
    __metadata("design:type", plano_conta_entity_1.PlanoConta)
], LancamentoFinanceiro.prototype, "planoContaCreditoEntity", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => plano_conta_entity_1.PlanoConta),
    (0, typeorm_1.JoinColumn)({ name: 'PLANO_CONTA_DEBITO' }),
    __metadata("design:type", plano_conta_entity_1.PlanoConta)
], LancamentoFinanceiro.prototype, "planoContaDebitoEntity", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => financeiro_centro_custo_entity_1.FinanceiroCentroCusto, financeiroCentroCusto => financeiroCentroCusto.lancamentoFinanceiro),
    __metadata("design:type", Array)
], LancamentoFinanceiro.prototype, "financeiroCentroCustos", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_ALT',
        type: 'datetime',
        precision: 6,
        nullable: false,
    }),
    __metadata("design:type", Date)
], LancamentoFinanceiro.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_DEL',
        type: 'datetime',
        precision: 6,
        nullable: true,
    }),
    __metadata("design:type", Date)
], LancamentoFinanceiro.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_INC',
        type: 'datetime',
        precision: 6,
        nullable: false,
    }),
    __metadata("design:type", Date)
], LancamentoFinanceiro.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_SYNC', type: 'datetime', precision: 6, nullable: true }),
    __metadata("design:type", Date)
], LancamentoFinanceiro.prototype, "dataSync", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_EXCLUIDO', length: 1, nullable: false, default: 'N' }),
    __metadata("design:type", String)
], LancamentoFinanceiro.prototype, "isExcluido", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_ALT', length: 500, nullable: false }),
    __metadata("design:type", String)
], LancamentoFinanceiro.prototype, "usuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_DEL', length: 500, nullable: true }),
    __metadata("design:type", String)
], LancamentoFinanceiro.prototype, "usuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_INC', length: 500, nullable: false }),
    __metadata("design:type", String)
], LancamentoFinanceiro.prototype, "usuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UUID', length: 50, nullable: true }),
    __metadata("design:type", String)
], LancamentoFinanceiro.prototype, "uuid", void 0);
exports.LancamentoFinanceiro = LancamentoFinanceiro = __decorate([
    (0, typeorm_1.Entity)({ name: 'LANCAMENTO_FINANCEIRO' })
], LancamentoFinanceiro);
//# sourceMappingURL=lancamento-financeiro.entity.js.map