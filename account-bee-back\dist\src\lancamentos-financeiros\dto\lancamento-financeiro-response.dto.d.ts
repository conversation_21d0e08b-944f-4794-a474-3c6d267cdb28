export declare class CostCenterAllocationResponseDto {
    id: number;
    centroCustoId: number;
    centroCustoNome: string;
    valor: number;
    porcentagem?: number;
}
export declare class LancamentoFinanceiroResponseDto {
    id: number;
    descricao: string;
    dataLancamento?: Date;
    dataCompetencia?: Date;
    valorBruto?: number;
    valor: number;
    observacao?: string;
    efetivado?: boolean;
    conciliado?: boolean;
    empresaId: number;
    pessoaId: number;
    pessoaNome?: string;
    contaId?: number;
    contaNome?: string;
    localId?: number;
    localNome?: string;
    fornecedorId?: number;
    fornecedorNome?: string;
    tipoLancamentoFinanceiroId: number;
    tipoLancamento?: string;
    categoriaLctoFinanceiroId?: number;
    categoriaNome?: string;
    planoContaCredito?: number;
    planoContaCreditoNome?: string;
    alocacoesCentroCusto?: CostCenterAllocationResponseDto[];
    dataHoraUsuarioInc: Date;
    dataHoraUsuarioAlt: Date;
    usuarioInc: string;
    usuarioAlt: string;
}
