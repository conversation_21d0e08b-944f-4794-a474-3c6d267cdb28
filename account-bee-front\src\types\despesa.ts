export interface CostCenterAllocation {
  centroCustoId: number;
  centroCustoNome?: string;
  valor: number;
  porcentagem?: number;
}

export interface CreateDespesa {
  // Aba Básico
  pessoaId: number;
  descricao: string;
  dataPagamento?: string;
  dataCompetencia?: string;
  contaId?: number;
  localId?: number;

  // Aba Financeiro
  valor: number; // Apenas um campo valor (sem distinção bruto/líquido)
  categoriaLctoFinanceiroId?: number;
  alocacoesCentroCusto?: CostCenterAllocation[];

  // Aba Adicional
  planoContaDebito?: number; // Para despesas usamos débito ao invés de crédito
  fornecedorId?: number;
  observacao?: string;

  // Campos de repetição
  repetirDespesa?: boolean;
  tipoRepeticao?: 'este' | 'esteEProximos';
  periodicidade?: 'mensal' | 'semanal' | 'anual';
  quantidadeRepeticoes?: number;
}

export interface Despesa {
  id: number;
  descricao: string;
  valor: number;
  dataPagamento?: string;
  dataCompetencia?: string;
  observacao?: string;
  efetivado?: boolean;
  
  // Relacionamentos
  empresaId: number;
  pessoaId: number;
  contaId?: number;
  localId?: number;
  fornecedorId?: number;
  categoriaLctoFinanceiroId?: number;
  planoContaDebito?: number;
  
  // Dados relacionados
  pessoa?: {
    id: number;
    nome: string;
    email?: string;
    cpf?: string;
    cnpj?: string;
  };
  
  conta?: {
    id: number;
    descricao: string;
  };
  
  local?: {
    id: number;
    descricao: string;
  };
  
  fornecedor?: {
    id: number;
    descricao: string;
  };
  
  categoria?: {
    id: number;
    descricao: string;
  };
  
  planoContaDebitoEntity?: {
    id: number;
    descricao: string;
  };
  
  alocacoesCentroCusto?: CostCenterAllocation[];
  
  // Auditoria
  dataHoraUsuarioInc: string;
  usuarioInc: string;
  dataHoraUsuarioAlt?: string;
  usuarioAlt?: string;
}

export interface DespesaResponse {
  id: number;
  descricao: string;
  valor: number;
  dataPagamento?: string;
  dataCompetencia?: string;
  observacao?: string;
  efetivado?: boolean;
  empresaId: number;
  pessoaId: number;
  contaId?: number;
  localId?: number;
  fornecedorId?: number;
  categoriaLctoFinanceiroId?: number;
  planoContaDebito?: number;
  dataHoraUsuarioInc: string;
  usuarioInc: string;
  dataHoraUsuarioAlt?: string;
  usuarioAlt?: string;
}

// Tipos para filtros e listagem
export interface DespesaFilters {
  dataInicio?: string;
  dataFim?: string;
  pessoaId?: number;
  contaId?: number;
  localId?: number;
  categoriaId?: number;
  fornecedorId?: number;
  efetivado?: boolean;
  valorMinimo?: number;
  valorMaximo?: number;
  descricao?: string;
}

export interface DespesaListResponse {
  data: Despesa[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Tipos para validação de formulário
export interface DespesaFormData {
  pessoaId?: number;
  descricao: string;
  valor?: number;
  dataPagamento: string;
  dataCompetencia?: string;
  contaId?: number;
  localId?: number;
  categoriaLctoFinanceiroId?: number;
  planoContaDebito?: number;
  fornecedorId?: number;
  observacao?: string;
  repetirDespesa?: boolean;
  tipoRepeticao?: 'este' | 'esteEProximos';
  periodicidade?: 'mensal' | 'semanal' | 'anual';
  quantidadeRepeticoes?: number;
}

// Tipos para estatísticas e resumos
export interface DespesaResumo {
  totalDespesas: number;
  valorTotal: number;
  despesasPorCategoria: Array<{
    categoriaId: number;
    categoriaNome: string;
    valor: number;
    quantidade: number;
  }>;
  despesasPorFornecedor: Array<{
    fornecedorId: number;
    fornecedorNome: string;
    valor: number;
    quantidade: number;
  }>;
}
