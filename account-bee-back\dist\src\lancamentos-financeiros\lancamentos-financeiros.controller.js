"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LancamentosFinanceirosController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const lancamentos_financeiros_service_1 = require("./lancamentos-financeiros.service");
const dto_1 = require("./dto");
let LancamentosFinanceirosController = class LancamentosFinanceirosController {
    lancamentosService;
    constructor(lancamentosService) {
        this.lancamentosService = lancamentosService;
    }
    async createReceita(createDto, req) {
        createDto.tipoLancamentoFinanceiroId = 1;
        createDto.empresaId = req.user.empresaId;
        return this.lancamentosService.create(createDto, req.user);
    }
    async createDespesa(createDespesaDto, req) {
        const createDto = {
            ...createDespesaDto,
            dataLancamento: createDespesaDto.dataPagamento,
            planoContaCredito: createDespesaDto.planoContaCredito,
            repetirReceita: createDespesaDto.repetirDespesa,
            tipoLancamentoFinanceiroId: 2,
            empresaId: req.user.empresaId,
        };
        return this.lancamentosService.create(createDto, req.user);
    }
    async listar(tipo, req, mes, ano, page, limit) {
        try {
            console.log('🔍 Controller - Listando lançamentos:', { tipo, mes, ano, page, limit });
            console.log('🔍 Controller - Usuário:', req.user?.email, 'EmpresaId:', req.user?.empresaId);
            const empresaId = req.user.empresaId;
            const tipoLancamento = tipo === 'receitas' ? 1 : 2;
            const filtros = {
                mes: mes ? parseInt(mes) : new Date().getMonth() + 1,
                ano: ano ? parseInt(ano) : new Date().getFullYear(),
                page: page ? parseInt(page) : 1,
                limit: limit ? parseInt(limit) : 50,
                tipoLancamentoFinanceiroId: tipoLancamento,
            };
            console.log('🔍 Controller - Filtros processados:', filtros);
            const resultado = await this.lancamentosService.listar(empresaId, filtros);
            console.log('✅ Controller - Resultado obtido:', { total: resultado.total, dataLength: resultado.data?.length });
            return resultado;
        }
        catch (error) {
            console.error('❌ Controller - Erro ao listar lançamentos:', error);
            throw error;
        }
    }
    async calcularValores(tipo, req, mes, ano) {
        const empresaId = req.user.empresaId;
        const tipoLancamento = tipo === 'receitas' ? 1 : 2;
        const filtros = {
            mes: mes ? parseInt(mes) : new Date().getMonth() + 1,
            ano: ano ? parseInt(ano) : new Date().getFullYear(),
            tipoLancamentoFinanceiroId: tipoLancamento,
        };
        return this.lancamentosService.calcularValores(empresaId, filtros);
    }
    async listarExtrato(req, mes, ano, page, limit) {
        try {
            console.log('🔍 Controller - Listando extrato unificado:', { mes, ano, page, limit });
            const empresaId = req.user.empresaId;
            const filtros = {
                mes: mes ? parseInt(mes) : new Date().getMonth() + 1,
                ano: ano ? parseInt(ano) : new Date().getFullYear(),
                page: page ? parseInt(page) : 1,
                limit: limit ? parseInt(limit) : 50,
            };
            console.log('🔍 Controller - Filtros processados:', filtros);
            const resultado = await this.lancamentosService.listarExtrato(empresaId, filtros);
            console.log('✅ Controller - Extrato obtido:', { total: resultado.total, dataLength: resultado.data?.length });
            return resultado;
        }
        catch (error) {
            console.error('❌ Controller - Erro ao listar extrato:', error);
            throw error;
        }
    }
    async alterarStatusEfetivado(id, body, req) {
        try {
            console.log('🔍 Controller - Alterando status efetivado:', { id, efetivado: body.efetivado });
            const empresaId = req.user.empresaId;
            const resultado = await this.lancamentosService.alterarStatusEfetivado(+id, body.efetivado, empresaId, req.user);
            console.log('✅ Controller - Status alterado com sucesso');
            return resultado;
        }
        catch (error) {
            console.error('❌ Controller - Erro ao alterar status:', error);
            throw error;
        }
    }
    async findOne(id, req) {
        const empresaId = req.user.empresaId;
        return this.lancamentosService.findById(+id, empresaId);
    }
    async updateCostCenterAllocations(id, allocations, req) {
        const empresaId = req.user.empresaId;
        return this.lancamentosService.updateCostCenterAllocations(+id, allocations, empresaId, req.user);
    }
    async getCostCenterAllocations(id, req) {
        const empresaId = req.user.empresaId;
        return this.lancamentosService.getCostCenterAllocations(+id, empresaId);
    }
    async deleteCostCenterAllocation(lancamentoId, allocationId, req) {
        const empresaId = req.user.empresaId;
        return this.lancamentosService.deleteCostCenterAllocation(+lancamentoId, +allocationId, empresaId, req.user);
    }
};
exports.LancamentosFinanceirosController = LancamentosFinanceirosController;
__decorate([
    (0, common_1.Post)('receitas'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova receita (lançamento financeiro tipo 1)' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Receita criada com sucesso',
        type: dto_1.LancamentoFinanceiroResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateLancamentoFinanceiroDto, Object]),
    __metadata("design:returntype", Promise)
], LancamentosFinanceirosController.prototype, "createReceita", null);
__decorate([
    (0, common_1.Post)('despesas'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova despesa (lançamento financeiro tipo 2)' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Despesa criada com sucesso',
        type: dto_1.LancamentoFinanceiroResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateDespesaDto, Object]),
    __metadata("design:returntype", Promise)
], LancamentosFinanceirosController.prototype, "createDespesa", null);
__decorate([
    (0, common_1.Get)('listar/:tipo'),
    (0, swagger_1.ApiOperation)({ summary: 'Listar lançamentos financeiros por tipo (receitas ou despesas)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de lançamentos financeiros',
        type: [dto_1.LancamentoFinanceiroResponseDto]
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('tipo')),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Query)('mes')),
    __param(3, (0, common_1.Query)('ano')),
    __param(4, (0, common_1.Query)('page')),
    __param(5, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String, String, String, String]),
    __metadata("design:returntype", Promise)
], LancamentosFinanceirosController.prototype, "listar", null);
__decorate([
    (0, common_1.Get)('calcular-valores/:tipo'),
    (0, swagger_1.ApiOperation)({ summary: 'Calcular valores totais de despesas e receitas' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Valores calculados',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('tipo')),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Query)('mes')),
    __param(3, (0, common_1.Query)('ano')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String, String]),
    __metadata("design:returntype", Promise)
], LancamentosFinanceirosController.prototype, "calcularValores", null);
__decorate([
    (0, common_1.Get)('extrato/listar'),
    (0, swagger_1.ApiOperation)({ summary: 'Listar extrato unificado (receitas e despesas)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Extrato unificado',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('mes')),
    __param(2, (0, common_1.Query)('ano')),
    __param(3, (0, common_1.Query)('page')),
    __param(4, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String]),
    __metadata("design:returntype", Promise)
], LancamentosFinanceirosController.prototype, "listarExtrato", null);
__decorate([
    (0, common_1.Patch)(':id/efetivado'),
    (0, swagger_1.ApiOperation)({ summary: 'Alterar status efetivado do lançamento financeiro' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Status alterado com sucesso',
        type: dto_1.LancamentoFinanceiroResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Lançamento financeiro não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], LancamentosFinanceirosController.prototype, "alterarStatusEfetivado", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lançamento financeiro por ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do lançamento financeiro' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lançamento encontrado',
        type: dto_1.LancamentoFinanceiroResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Lançamento não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], LancamentosFinanceirosController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id/alocacoes-centro-custo'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar alocações de centro de custo de um lançamento' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do lançamento financeiro' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Alocações atualizadas com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Lançamento não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Array, Object]),
    __metadata("design:returntype", Promise)
], LancamentosFinanceirosController.prototype, "updateCostCenterAllocations", null);
__decorate([
    (0, common_1.Get)(':id/alocacoes-centro-custo'),
    (0, swagger_1.ApiOperation)({ summary: 'Listar alocações de centro de custo de um lançamento' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do lançamento financeiro' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Lista de alocações' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Lançamento não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], LancamentosFinanceirosController.prototype, "getCostCenterAllocations", null);
__decorate([
    (0, common_1.Delete)(':lancamentoId/alocacoes-centro-custo/:allocationId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Excluir uma alocação de centro de custo específica' }),
    (0, swagger_1.ApiParam)({ name: 'lancamentoId', description: 'ID do lançamento financeiro' }),
    (0, swagger_1.ApiParam)({ name: 'allocationId', description: 'ID da alocação de centro de custo' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Alocação excluída com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Alocação não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('lancamentoId')),
    __param(1, (0, common_1.Param)('allocationId')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], LancamentosFinanceirosController.prototype, "deleteCostCenterAllocation", null);
exports.LancamentosFinanceirosController = LancamentosFinanceirosController = __decorate([
    (0, swagger_1.ApiTags)('lancamentos-financeiros'),
    (0, common_1.Controller)('lancamentos-financeiros'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [lancamentos_financeiros_service_1.LancamentosFinanceirosService])
], LancamentosFinanceirosController);
//# sourceMappingURL=lancamentos-financeiros.controller.js.map