import { LancamentosFinanceirosService } from './lancamentos-financeiros.service';
import { CreateLancamentoFinanceiroDto, CreateDespesaDto, LancamentoFinanceiroResponseDto, CostCenterAllocationDto } from './dto';
export declare class LancamentosFinanceirosController {
    private readonly lancamentosService;
    constructor(lancamentosService: LancamentosFinanceirosService);
    createReceita(createDto: CreateLancamentoFinanceiroDto, req: any): Promise<LancamentoFinanceiroResponseDto>;
    createDespesa(createDespesaDto: CreateDespesaDto, req: any): Promise<LancamentoFinanceiroResponseDto>;
    listar(tipo: 'receitas' | 'despesas', req: any, mes?: string, ano?: string, page?: string, limit?: string): Promise<any>;
    calcularValores(tipo: 'receitas' | 'despesas', req: any, mes?: string, ano?: string): Promise<any>;
    listarExtrato(req: any, mes?: string, ano?: string, page?: string, limit?: string): Promise<any>;
    alterarStatusEfetivado(id: string, body: {
        efetivado: boolean;
    }, req: any): Promise<LancamentoFinanceiroResponseDto>;
    findOne(id: string, req: any): Promise<LancamentoFinanceiroResponseDto>;
    updateCostCenterAllocations(id: string, allocations: CostCenterAllocationDto[], req: any): Promise<void>;
    getCostCenterAllocations(id: string, req: any): Promise<any[]>;
    deleteCostCenterAllocation(lancamentoId: string, allocationId: string, req: any): Promise<void>;
}
