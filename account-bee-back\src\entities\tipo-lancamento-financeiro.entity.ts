import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'TIPO_LANCAMENTO_FINANCEIRO' })
export class TipoLancamentoFinanceiro {
  @PrimaryGeneratedColumn({ name: 'ID' })
  id: number;

  @Column({ name: 'DESCRICAO', length: 200, nullable: false })
  descricao: string;

  // Enum de tipos conforme sistema legado
  // 1 = Receita
  // 2 = Despesa
  // 3 = Transferência
  // 4 = Saldo Inicial
}
