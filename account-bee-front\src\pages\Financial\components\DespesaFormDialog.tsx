import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Upload } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/shadcn/dialog';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/shadcn/tabs';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/forms';
import { Input, CurrencyInput, Select, DateInput } from '@/components/ui/forms';
import { Button } from '@/components/ui/forms';
import { Textarea } from '@/components/ui/shadcn/textarea';
import { Checkbox } from '@/components/ui/forms';
import { RadioGroup, RadioGroupItem } from '@/components/ui/forms';
import { Label } from '@/components/ui/forms';
import { CostCenterApportionment } from '@/components/ui/forms';
import { usePeople } from '@/hooks/usePeople';
import { useLocations } from '@/hooks/useLocations';
import { useCentrosCusto } from '@/hooks/useCentrosCusto';
import { useCategoriasLancamento } from '@/hooks/useCategoriasLancamento';
import { usePlanoContas } from '@/hooks/usePlanoContas';
import { useFornecedores } from '@/hooks/useFornecedores';
import { useLancamentosFinanceiros } from '@/hooks/useLancamentosFinanceiros';
import { useContas } from '@/hooks/useContas';
import { useToast } from '@/hooks/use-toast';

const costCenterAllocationSchema = z.object({
  id: z.string().optional(),
  costCenterId: z.number().optional(),
  costCenterName: z.string().optional(),
  value: z.number().min(0).optional(),
  percentage: z.number().min(0).max(100).optional(),
});

const despesaSchema = z.object({
  // Aba Básico - Campos obrigatórios
  pessoaId: z.number().min(1, 'Cliente/Pessoa é obrigatório'),
  descricao: z.string().min(1, 'Descrição é obrigatória'),
  dataPagamento: z.string().optional(),
  dataCompetencia: z.string().optional(),
  contaId: z.number().min(1, 'Conta é obrigatória'), // Obrigatório conforme especificação
  localId: z.number().optional(),

  // Aba Financeiro - Apenas um campo valor (sem valorBruto)
  valor: z.number().min(0.01, 'Valor deve ser maior que zero'), // Obrigatório
  categoriaLctoFinanceiroId: z.number().optional(),
  costCenterAllocations: z.array(costCenterAllocationSchema).optional(),
  repetirDespesa: z.boolean().optional(),
  tipoRepeticao: z.enum(['este', 'esteEProximos']).optional(),
  periodicidade: z.enum(['mensal', 'semanal', 'anual']).optional(),
  quantidadeRepeticoes: z.number().min(1).optional(),

  // Aba Adicional
  planoContaCredito: z.number().optional(),
  fornecedorId: z.number().optional(),
  observacao: z.string().optional(),
});

type DespesaFormData = z.infer<typeof despesaSchema>;

interface DespesaFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  despesa?: any | null;
  onSuccess: () => void;
}

export function DespesaFormDialog({ open, onOpenChange, despesa, onSuccess }: DespesaFormDialogProps) {
  const { people } = usePeople();
  const { locations } = useLocations();
  const { centrosCusto } = useCentrosCusto();
  const { categorias } = useCategoriasLancamento();
  const { planoContas } = usePlanoContas();
  const { fetchFornecedoresDropdown } = useFornecedores();
  const { createDespesa, loading } = useLancamentosFinanceiros();
  const { contas } = useContas();
  const { toast } = useToast();

  const [fornecedores, setFornecedores] = useState<any[]>([]);

  const form = useForm<DespesaFormData>({
    resolver: zodResolver(despesaSchema),
    defaultValues: {
      pessoaId: undefined,
      descricao: '',
      dataPagamento: '',
      dataCompetencia: '',
      contaId: undefined,
      localId: undefined,
      valor: undefined,
      categoriaLctoFinanceiroId: undefined,
      costCenterAllocations: [],
      repetirDespesa: false,
      tipoRepeticao: 'este',
      periodicidade: 'mensal',
      quantidadeRepeticoes: 1,
      planoContaCredito: undefined,
      fornecedorId: undefined,
      observacao: '',
    },
  });

  // Carregar fornecedores quando o modal abrir e resetar formulário com dados da despesa clonada
  useEffect(() => {
    if (open) {
      loadFornecedores();
      
      // Se há dados de despesa clonada, preencher o formulário
      if (despesa) {
        const formData = {
          pessoaId: despesa.pessoaId || undefined,
          descricao: despesa.descricao || '',
          dataPagamento: despesa.dataPagamento || '',
          dataCompetencia: despesa.dataCompetencia || '',
          contaId: despesa.contaId || undefined,
          localId: despesa.localId || undefined,
          valor: despesa.valor || undefined,
          categoriaLctoFinanceiroId: despesa.categoriaLctoFinanceiroId || undefined,
          costCenterAllocations: despesa.alocacoesCentroCusto?.map((alocacao: any) => ({
            id: Math.random().toString(36).substring(2, 11),
            costCenterId: alocacao.centroCustoId,
            costCenterName: alocacao.centroCusto?.nome || '',
            value: alocacao.valor,
            percentage: alocacao.porcentagem,
          })) || [],
          repetirDespesa: false, // Não repetir por padrão em clonagem
          tipoRepeticao: 'este' as const,
          periodicidade: 'mensal' as const,
          quantidadeRepeticoes: 1,
          planoContaCredito: despesa.planoContaCredito || undefined,
          fornecedorId: despesa.fornecedorId || undefined,
          observacao: despesa.observacao || '',
        };
        
        // Reset do formulário com os dados clonados
        form.reset(formData);
      } else {
        // Reset com valores padrão se não há despesa para clonar
        form.reset({
          pessoaId: undefined,
          descricao: '',
          dataPagamento: '',
          dataCompetencia: '',
          contaId: undefined,
          localId: undefined,
          valor: undefined,
          categoriaLctoFinanceiroId: undefined,
          costCenterAllocations: [],
          repetirDespesa: false,
          tipoRepeticao: 'este',
          periodicidade: 'mensal',
          quantidadeRepeticoes: 1,
          planoContaCredito: undefined,
          fornecedorId: undefined,
          observacao: '',
        });
      }
    }
  }, [open, despesa, form]);

  const loadFornecedores = async () => {
    try {
      const fornecedoresData = await fetchFornecedoresDropdown();
      setFornecedores(fornecedoresData);
    } catch (error) {
      console.error('Erro ao carregar fornecedores:', error);
    }
  };

  const onSubmit = async (data: DespesaFormData) => {
    try {
      // Remover costCenterAllocations do data para evitar conflito
      const { costCenterAllocations, ...dataWithoutCostCenter } = data;

      const despesaData = {
        ...dataWithoutCostCenter,
        // Converter alocações de centro de custo para o formato esperado pelo backend
        alocacoesCentroCusto: costCenterAllocations?.map(allocation => ({
          centroCustoId: allocation.costCenterId, // Já é number
          valor: allocation.value,
          porcentagem: allocation.percentage,
        })),
        // Configurar repetição apenas se solicitada
        repetirDespesa: data.repetirDespesa,
        tipoRepeticao: data.repetirDespesa ? data.tipoRepeticao : undefined,
        periodicidade: data.repetirDespesa ? data.periodicidade : undefined,
        quantidadeRepeticoes: data.repetirDespesa && data.tipoRepeticao === 'esteEProximos'
          ? data.quantidadeRepeticoes
          : undefined,
      };

      await createDespesa(despesaData);

      toast({
        title: 'Despesa criada com sucesso',
        description: 'A despesa foi registrada no sistema.',
      });

      onSuccess();
      onOpenChange(false);
      form.reset();
    } catch (error) {
      toast({
        title: 'Erro ao criar despesa',
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: 'destructive',
      });
    }
  };

  const handleCostCenterAllocationsChange = (allocations: any[]) => {
    form.setValue('costCenterAllocations', allocations);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {despesa ? 'Editar Despesa' : 'Nova Despesa'}
          </DialogTitle>
          <DialogDescription>
            Preencha os dados da despesa nas abas abaixo.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="basico" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basico">Básico</TabsTrigger>
                <TabsTrigger value="financeiro">Financeiro</TabsTrigger>
                <TabsTrigger value="adicional">Adicional</TabsTrigger>
              </TabsList>

              <TabsContent value="basico" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="pessoaId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cliente / Pessoa *</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString() || ''}
                            onValueChange={(value) => field.onChange(parseInt(value))}
                            placeholder="Selecione um cliente"
                            options={people.map((person) => ({
                              value: person.id.toString(),
                              label: person.nome,
                            }))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="descricao"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Descrição *</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Ex: Caju Benefícios" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="dataPagamento"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Data de Pagamento</FormLabel>
                        <FormControl>
                          <DateInput
                            value={field.value ? new Date(field.value) : undefined}
                            onChange={(date) => field.onChange(date ? date.toISOString().split('T')[0] : '')}
                            placeholder="Selecione a data de pagamento"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="dataCompetencia"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Data de Competência</FormLabel>
                        <FormControl>
                          <DateInput
                            value={field.value ? new Date(field.value) : undefined}
                            onChange={(date) => field.onChange(date ? date.toISOString().split('T')[0] : '')}
                            placeholder="Selecione a data de competência"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="contaId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Conta *</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString() || ''}
                            onValueChange={(value) => field.onChange(parseInt(value))}
                            placeholder="Selecione uma conta"
                            options={contas.map((conta) => ({
                              value: conta.id.toString(),
                              label: conta.descricao,
                            }))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="localId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Local</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString() || ''}
                            onValueChange={(value) => field.onChange(parseInt(value))}
                            placeholder="Selecione um local"
                            options={locations.map((location) => ({
                              value: location.id.toString(),
                              label: location.nome,
                            }))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              <TabsContent value="financeiro" className="space-y-4">
                <FormField
                  control={form.control}
                  name="valor"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valor *</FormLabel>
                      <FormControl>
                        <CurrencyInput
                          value={field.value || 0}
                          onChange={field.onChange}
                          placeholder="R$ 0,00"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="categoriaLctoFinanceiroId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Categoria</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value?.toString() || ''}
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          placeholder={categorias.length > 0 ? "Selecione uma categoria" : "Nenhuma categoria encontrada"}
                          options={categorias.length > 0 ? categorias.map((categoria) => ({
                            value: categoria.id.toString(),
                            label: categoria.descricao,
                          })) : [{ value: 'no-data', label: 'Nenhuma categoria cadastrada', disabled: true }]}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div>
                  <Label>Alocação por Centro de Custo</Label>
                  <CostCenterApportionment
                    costCenters={centrosCusto}
                    totalAmount={form.watch('valor') || 0}
                    allocations={(form.watch('costCenterAllocations') || []).map(allocation => ({
                      id: allocation.id || Math.random().toString(36).substring(2, 11),
                      costCenterId: allocation.costCenterId || 0,
                      costCenterName: allocation.costCenterName || '',
                      value: allocation.value || 0,
                      percentage: allocation.percentage || 0,
                    }))}
                    onAllocationsChange={handleCostCenterAllocationsChange}
                  />
                </div>

                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="repetirDespesa"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Repetir Receita</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />

                  {form.watch('repetirDespesa') && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="tipoRepeticao"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Tipo de Repetição</FormLabel>
                              <FormControl>
                                <RadioGroup
                                  value={field.value}
                                  onValueChange={field.onChange}
                                  className="flex flex-col space-y-1"
                                >
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem value="este" id="este" />
                                    <Label htmlFor="este">Apenas Este</Label>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem value="esteEProximos" id="esteEProximos" />
                                    <Label htmlFor="esteEProximos">Este e os Próximos</Label>
                                  </div>
                                </RadioGroup>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="periodicidade"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Periodicidade</FormLabel>
                              <FormControl>
                                <Select
                                  value={field.value}
                                  onValueChange={field.onChange}
                                  placeholder="Selecione a periodicidade"
                                  options={[
                                    { value: 'mensal', label: 'Mensal' },
                                    { value: 'semanal', label: 'Semanal' },
                                    { value: 'anual', label: 'Anual' },
                                  ]}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {form.watch('tipoRepeticao') === 'esteEProximos' && (
                        <FormField
                          control={form.control}
                          name="quantidadeRepeticoes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Quantidade de Repetições</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  value={field.value?.toString() || ''}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                  placeholder="Ex: 12 (para 12 meses)"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="adicional" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="planoContaCredito"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Plano Conta de Crédito</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString() || ''}
                            onValueChange={(value) => field.onChange(parseInt(value))}
                            placeholder={planoContas.length > 0 ? "Selecione um plano de conta" : "Nenhum plano de conta encontrado"}
                            options={planoContas.length > 0 ? planoContas.map((plano) => ({
                              value: plano.id.toString(),
                              label: plano.descricao,
                            })) : [{ value: 'no-data', label: 'Nenhum plano de conta cadastrado', disabled: true }]}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="fornecedorId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Prestador (Fornecedor)</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString() || ''}
                            onValueChange={(value) => field.onChange(parseInt(value))}
                            placeholder="Selecione um fornecedor"
                            options={fornecedores.map((fornecedor) => ({
                              value: fornecedor.id.toString(),
                              label: fornecedor.descricao,
                            }))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="observacao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Observação</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Observações sobre a receita..."
                          rows={4}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <Label className="text-sm text-gray-600">
                      Anexar Arquivo (Clique para fazer upload ou arraste arquivos aqui)
                    </Label>
                    <p className="text-xs text-gray-500 mt-1">
                      Funcionalidade será implementada em breve
                    </p>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Salvando...' : despesa ? 'Atualizar' : 'Criar Despesa'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
