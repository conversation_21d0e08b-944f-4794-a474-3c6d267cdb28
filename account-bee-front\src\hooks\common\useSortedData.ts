/**
 * Hook para gerenciamento de dados com ordenação padrão
 * Aplica a mesma ordenação do backend:
 * 1. dataHoraUsuarioInc DESC (mais recente primeiro)
 * 2. dataHoraUsuarioAlt DESC
 * 3. id DESC
 */

import { useMemo } from 'react';

/**
 * Interface para entidades que possuem campos de auditoria
 */
export interface SortableEntity {
  id: string | number;
  createdAt?: string; // Correspondente a dataHoraUsuarioInc
  updatedAt?: string; // Correspondente a dataHoraUsuarioAlt
  dataHoraUsuarioInc?: string; // Campo direto do backend
  dataHoraUsuarioAlt?: string; // Campo direto do backend
}

/**
 * Função utilitária para obter a data de criação de uma entidade
 */
const getCreatedDate = (item: SortableEntity): Date => {
  const dateStr = item.dataHoraUsuarioInc || item.createdAt;
  return dateStr ? new Date(dateStr) : new Date(0);
};

/**
 * Função utilitária para obter a data de alteração de uma entidade
 */
const getUpdatedDate = (item: SortableEntity): Date => {
  const dateStr = item.dataHoraUsuarioAlt || item.updatedAt;
  return dateStr ? new Date(dateStr) : new Date(0);
};

/**
 * Função utilitária para obter o ID numérico de uma entidade
 */
const getNumericId = (item: SortableEntity): number => {
  const id = typeof item.id === 'string' ? parseInt(item.id, 10) : item.id;
  return isNaN(id) ? 0 : id;
};

/**
 * Função de ordenação padrão baseada na lógica do backend
 */
export const defaultSortComparator = <T extends SortableEntity>(a: T, b: T): number => {
  // 1. Comparar por dataHoraUsuarioInc DESC (mais recente primeiro)
  const aCreated = getCreatedDate(a);
  const bCreated = getCreatedDate(b);
  
  if (aCreated.getTime() !== bCreated.getTime()) {
    return bCreated.getTime() - aCreated.getTime(); // DESC
  }

  // 2. Comparar por dataHoraUsuarioAlt DESC
  const aUpdated = getUpdatedDate(a);
  const bUpdated = getUpdatedDate(b);
  
  if (aUpdated.getTime() !== bUpdated.getTime()) {
    return bUpdated.getTime() - aUpdated.getTime(); // DESC
  }

  // 3. Comparar por id DESC
  const aId = getNumericId(a);
  const bId = getNumericId(b);
  
  return bId - aId; // DESC
};

/**
 * Hook para ordenar dados automaticamente usando a ordenação padrão
 * @param data Array de dados para ordenar
 * @param customSortFn Função de ordenação customizada (opcional)
 * @returns Array ordenado
 */
export const useSortedData = <T extends SortableEntity>(
  data: T[],
  customSortFn?: (a: T, b: T) => number
): T[] => {
  return useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    // Se os dados já estão ordenados, retornar sem reordenar
    if (data.length <= 1) {
      return data;
    }

    const sortFn = customSortFn || defaultSortComparator;
    
    // Verificar se já está ordenado para evitar reordenação desnecessária
    let isAlreadySorted = true;
    for (let i = 0; i < data.length - 1; i++) {
      if (sortFn(data[i], data[i + 1]) > 0) {
        isAlreadySorted = false;
        break;
      }
    }
    
    if (isAlreadySorted) {
      return data;
    }
    
    return [...data].sort(sortFn);
  }, [data, customSortFn]);
};

/**
 * Hook para inserir novos itens no início da lista mantendo a ordenação
 * @param currentData Lista atual de dados
 * @param newItem Novo item a ser inserido
 * @returns Nova lista com o item inserido na posição correta
 */
export const useInsertSorted = <T extends SortableEntity>() => {
  const insertSorted = (currentData: T[], newItem: T): T[] => {
    // Se a lista está vazia, retornar apenas o novo item
    if (currentData.length === 0) {
      return [newItem];
    }
    
    // Se o novo item deve ir no início (mais recente), inserir no início
    const firstItem = currentData[0];
    if (defaultSortComparator(newItem, firstItem) <= 0) {
      return [newItem, ...currentData];
    }
    
    // Caso contrário, inserir na posição correta
    const newData = [newItem, ...currentData];
    return newData.sort(defaultSortComparator);
  };

  const insertMultipleSorted = (currentData: T[], newItems: T[]): T[] => {
    if (newItems.length === 0) {
      return currentData;
    }
    
    if (currentData.length === 0) {
      return newItems.sort(defaultSortComparator);
    }
    
    const newData = [...newItems, ...currentData];
    return newData.sort(defaultSortComparator);
  };

  return {
    insertSorted,
    insertMultipleSorted,
  };
};

/**
 * Hook para atualizar um item na lista mantendo a ordenação
 * @param currentData Lista atual de dados
 * @param updatedItem Item atualizado
 * @param idField Campo usado como chave (padrão: 'id')
 * @returns Nova lista com o item atualizado na posição correta
 */
export const useUpdateSorted = <T extends SortableEntity>() => {
  const updateSorted = (
    currentData: T[],
    updatedItem: T,
    idField: keyof T = 'id'
  ): T[] => {
    const newData = currentData.map(item => 
      item[idField] === updatedItem[idField] ? updatedItem : item
    );
    
    // Verificar se a ordenação mudou
    const oldItem = currentData.find(item => item[idField] === updatedItem[idField]);
    if (oldItem && defaultSortComparator(oldItem, updatedItem) === 0) {
      // Se a ordenação não mudou, retornar sem reordenar
      return newData;
    }
    
    return newData.sort(defaultSortComparator);
  };

  return { updateSorted };
};

/**
 * Utilitários para trabalhar com dados ordenados
 */
export const sortUtils = {
  /**
   * Verifica se uma lista está ordenada corretamente
   */
  isCorrectlySorted: <T extends SortableEntity>(data: T[]): boolean => {
    if (data.length <= 1) return true;
    
    for (let i = 0; i < data.length - 1; i++) {
      if (defaultSortComparator(data[i], data[i + 1]) > 0) {
        return false;
      }
    }
    return true;
  },

  /**
   * Encontra a posição correta para inserir um novo item
   */
  findInsertPosition: <T extends SortableEntity>(data: T[], newItem: T): number => {
    for (let i = 0; i < data.length; i++) {
      if (defaultSortComparator(newItem, data[i]) <= 0) {
        return i;
      }
    }
    return data.length;
  },

  /**
   * Cria um timestamp atual para novos itens
   */
  createTimestamp: (): string => {
    return new Date().toISOString();
  },
};