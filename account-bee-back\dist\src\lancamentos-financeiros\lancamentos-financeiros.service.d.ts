import { Repository, DataSource } from 'typeorm';
import { LancamentoFinanceiro } from '../entities/lancamento-financeiro.entity';
import { FinanceiroCentroCusto } from '../entities/financeiro-centro-custo.entity';
import { FinancialCryptoService } from '../crypto/financial-crypto.service';
import { CreateLancamentoFinanceiroDto, LancamentoFinanceiroResponseDto, CostCenterAllocationDto } from './dto';
export declare class LancamentosFinanceirosService {
    private readonly lancamentoRepository;
    private readonly financeiroCentroCustoRepository;
    private readonly financialCryptoService;
    private readonly dataSource;
    constructor(lancamentoRepository: Repository<LancamentoFinanceiro>, financeiroCentroCustoRepository: Repository<FinanceiroCentroCusto>, financialCryptoService: FinancialCryptoService, dataSource: DataSource);
    create(createDto: CreateLancamentoFinanceiroDto, user: any): Promise<LancamentoFinanceiroResponseDto>;
    private validateRequiredFields;
    private validateCostCenterAllocations;
    private createCostCenterAllocations;
    private mapToResponseDto;
    findById(id: number, empresaId: number): Promise<LancamentoFinanceiroResponseDto>;
    updateCostCenterAllocations(lancamentoId: number, allocations: CostCenterAllocationDto[], empresaId: number, user: any): Promise<void>;
    deleteCostCenterAllocation(lancamentoId: number, allocationId: number, empresaId: number, user: any): Promise<void>;
    getCostCenterAllocations(lancamentoId: number, empresaId: number): Promise<any[]>;
    private createRecurringLancamentos;
    private calculateNextDate;
    listar(empresaId: number, filtros: any): Promise<any>;
    listarExtrato(empresaId: number, filtros: any): Promise<any>;
    alterarStatusEfetivado(id: number, efetivado: boolean, empresaId: number, usuario: any): Promise<LancamentoFinanceiroResponseDto>;
    calcularValores(empresaId: number, filtros: any): Promise<any>;
}
