"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TipoLancamentoFinanceiro = void 0;
const typeorm_1 = require("typeorm");
let TipoLancamentoFinanceiro = class TipoLancamentoFinanceiro {
    id;
    descricao;
};
exports.TipoLancamentoFinanceiro = TipoLancamentoFinanceiro;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], TipoLancamentoFinanceiro.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRICAO', length: 200, nullable: false }),
    __metadata("design:type", String)
], TipoLancamentoFinanceiro.prototype, "descricao", void 0);
exports.TipoLancamentoFinanceiro = TipoLancamentoFinanceiro = __decorate([
    (0, typeorm_1.Entity)({ name: 'TIPO_LANCAMENTO_FINANCEIRO' })
], TipoLancamentoFinanceiro);
//# sourceMappingURL=tipo-lancamento-financeiro.entity.js.map