"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDespesaDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
const create_lancamento_financeiro_dto_1 = require("./create-lancamento-financeiro.dto");
class CreateDespesaDto {
    pessoaId;
    descricao;
    valor;
    dataPagamento;
    dataCompetencia;
    contaId;
    localId;
    categoriaLctoFinanceiroId;
    alocacoesCentroCusto;
    planoContaCredito;
    fornecedorId;
    observacao;
    repetirDespesa;
    tipoRepeticao;
    periodicidade;
    quantidadeRepeticoes;
    empresaId;
    tipoLancamentoFinanceiroId;
}
exports.CreateDespesaDto = CreateDespesaDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID da pessoa/fornecedor' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Fornecedor/Pessoa é obrigatório' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateDespesaDto.prototype, "pessoaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Descrição da despesa', minLength: 1, maxLength: 200 }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Descrição é obrigatória' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDespesaDto.prototype, "descricao", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Valor da despesa', minimum: 0.01 }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Valor é obrigatório' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0.01, { message: 'Valor deve ser maior que zero' }),
    __metadata("design:type", Number)
], CreateDespesaDto.prototype, "valor", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Data de pagamento da despesa', format: 'date' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateDespesaDto.prototype, "dataPagamento", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Data de competência da despesa', format: 'date' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateDespesaDto.prototype, "dataCompetencia", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID da conta bancária/caixa' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateDespesaDto.prototype, "contaId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID do local/filial' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateDespesaDto.prototype, "localId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID da categoria do lançamento financeiro' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateDespesaDto.prototype, "categoriaLctoFinanceiroId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Alocações por centro de custo',
        type: [create_lancamento_financeiro_dto_1.CostCenterAllocationDto]
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => create_lancamento_financeiro_dto_1.CostCenterAllocationDto),
    __metadata("design:type", Array)
], CreateDespesaDto.prototype, "alocacoesCentroCusto", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID do plano de contas crédito' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateDespesaDto.prototype, "planoContaCredito", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID do fornecedor' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateDespesaDto.prototype, "fornecedorId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Observações adicionais', maxLength: 1000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDespesaDto.prototype, "observacao", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Indica se a despesa deve ser repetida' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (typeof value === 'string') {
            return value === 'true' || value === '1';
        }
        return Boolean(value);
    }),
    __metadata("design:type", Boolean)
], CreateDespesaDto.prototype, "repetirDespesa", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tipo de repetição',
        enum: ['este', 'esteEProximos']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDespesaDto.prototype, "tipoRepeticao", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Periodicidade da repetição',
        enum: ['mensal', 'semanal', 'anual']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDespesaDto.prototype, "periodicidade", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Quantidade de repetições', minimum: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateDespesaDto.prototype, "quantidadeRepeticoes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID da empresa (preenchido automaticamente)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateDespesaDto.prototype, "empresaId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tipo de lançamento financeiro (preenchido automaticamente como 2 para despesa)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateDespesaDto.prototype, "tipoLancamentoFinanceiroId", void 0);
//# sourceMappingURL=create-despesa.dto.js.map