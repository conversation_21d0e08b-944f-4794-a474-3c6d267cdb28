import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LancamentoFinanceiro } from '../entities/lancamento-financeiro.entity';
import { FinanceiroCentroCusto } from '../entities/financeiro-centro-custo.entity';
import { TipoLancamentoFinanceiro } from '../entities/tipo-lancamento-financeiro.entity';
import { CategoriaLancamento } from '../entities/categoria-lancamento.entity';
import { PlanoConta } from '../entities/plano-conta.entity';
import { LancamentosFinanceirosController } from './lancamentos-financeiros.controller';
import { LancamentosFinanceirosService } from './lancamentos-financeiros.service';
import { CryptoModule } from '../crypto/crypto.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LancamentoFinanceiro,
      FinanceiroCentroCusto,
      TipoLancamentoFinanceiro,
      CategoriaLancamento,
      PlanoConta,
    ]),
    CryptoModule,
  ],
  controllers: [LancamentosFinanceirosController],
  providers: [LancamentosFinanceirosService],
  exports: [LancamentosFinanceirosService],
})
export class LancamentosFinanceirosModule {}
